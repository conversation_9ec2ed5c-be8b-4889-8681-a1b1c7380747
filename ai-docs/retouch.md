
Retouch – Veri Yönetim Aracı Şartname Dokümanı
1\. <PERSON><PERSON><PERSON>, Retouch ürününe ait tüm fonksiyonel ve teknik gereksinimleri kapsar.
Retouch, veri giriş süreçlerini kolaylaştıran, validasyon kuralları ile veri kalitesini ver,
girişleri esnasında kontrol altına alan ve kullanıcı dostu arayüzü ile yapılandırılmış veri
işleme olanağı sunan bir web tabanlı platformdur.
2\. Genel Tanım
2.1. Ürün Özellikleri
• Yapılandırılmış ve kontrollü veri giriş arayüzü sunmak
• Hatalı veri girişini engelleyen kurallarla veri kalitesini artırmak
• Roller, yetkiler, onay mekanizmaları ile veri yönetim süreçlerini denetim altına
almak
• Geriye dönük izlenebilirlik sağlayarak güvenilir veri altyapısı oluşturmak
2.2. Fonksiyonel Olmayan Özellikler
• Kullanıcı dostu arayüz
• Oracle, MSSQL, PostgreSQL, Snowflake veritabanları ile uyumluluk
• Güvenli kimlik doğrulama ve oturum yönetimi
3\. Fonksiyonel Kapsam
3.1. Kullanıcı Tipleri ve Yetkiler
Yetki / Rol Sistem Admin Veri Sahibi Kullanıcı
Uygulama yönetimi ✔
Kullanıcı/rol yönetimi ✔
Veritabanı bağlantıları yönetimi ✔
Tablo Oluşturma ✔
Validasyon Kuralı Tanımlama ✔ ✔
3Yetki / Rol Sistem Admin Veri Sahibi Kullanıcı
Bildirim Tanımlama ✔ ✔
Form Ekranı Düzenleme ✔ ✔
Veri Görüntüleme & Düzenleme\&Ekleme
\&Düzenleme\&Silme
✔ ✔ ✔
Veri İçe & Dışa Aktarım ✔ ✔ ✔
3.2 Veri Tabanında Tablo Oluşturma Özelliği
• Uygulama üzerinden veri tabanında yeni tablo oluşturma ve düzenleme
• Mevcut veritabanı tabloları üzerinde kolon ekleme/çıkarma, tür değiştirme ve
silme işlemleri
• Tablo kolon listesinin dosyadan içe aktarımı
• Tabloda yer alan kolonların referans bir tablodaki veriler içerisinden seçiminin
tanımlanması
• Oluşturulan tabloların Retouch ekranlarındaki sıralamasının belirlenmesi
3.3 Klasör Yapısı
• Rol bazlı yetkilere göre klasör oluşturma ve görüntüleme
• Klasör bazlı yetkilendirme ile işlemlerin merkezi yönetimi
3.4 Veri Tabanındaki Tablonun Retouch Entegrasyonu (ResourceTable)
• Kullanılacak tabloların Retouch sistemine tanıtılması
• Tabloda yer alacak anahtar koşonların belirlenmesi
• Onaylayan tanımı ile veri onay süreçlerinin devreye alınması
• Tarihçe tutulup tutulmayacağının belirlenmesi ve görüntülenmesi
3.5 Retouch Veri Giriş & Düzenleme Ekran Yapısı (Retable kartı)
• Kullanıcı rolüne ait yetkilendirmeler doğrultusunda satır ve sütün bazında veri
görüntüleyebilme, yeni veri girme, mevcut veriyi güncelleme, veri silme, içe ve
dışa aktarım yapılabilmesi
• Sayfalama veya tam ekran çalışabilme özelliği
• Veri Girişleri
4➢ Manuel Veri Girişi
▪ Ekran üzerinden yeni bir satır açarak manuel veri girişi yapabilme
▪ Ekran üzerinde yeni açılan satırlara excel dosyasından kopyalanan
satırların yapıştırılabilmesi
▪ Validasyon kuralları ile belirlenen hatalı veri girişlerinin hücre
değişimlerinde tespit edilip kullanıcıya uyarı verilmesi
▪ Referans veri tanımlanan kolonların girişlerinde seçim listesinin
görüntülenmesi ve liste dışında seçim yapılmasına izin verilmemesi
➢ İçe Aktarım
▪ xls, xlsx,csv dosya türlerinin içeri alımının desteklenmesi
▪ İçe alım sırasındaki seçenekler;
❖ Sadece yeni kayıtları al, mevcut kayıtları güncelleme, hatalı
kayıtları dışa aktar
❖ Yeni kayıtları al, mevcut kayıtları listele, hatalı kayıtları dışa
aktar
❖ Hatalı kayıt varsa aktarım yapma, hatalı kayıtları dışa aktar
❖ Yeni kayıtları al, mevcut kayıtları listele, hatalı kayıtların
düzenlenmesi için ekranda göster
▪ Dosya içeri alınmadan önce ön izleme ile ekranda alınacak kayıtları
listeleme
▪ Dosya içeri alınmadan önce okunan kayıtların tamamını red
edebilme
▪ İçeri aktarım ile yeni kayıt sayısı ve güncellenecek kayıt sayısı
bildirimi
▪ Opsiyonel olarak hatalı olan kayıtların ekran üzerinde listelenip
düzenlenebilmesinin sağlanması
▪ Opsiyonel olarak hatalı kayıtların dışa aktarım ile kullanıcıya
sunulması
• Dışa aktarım yapılabilmesi
• Kolonlar üzerinde sıralama yapılabilmesi
• Kolonlar üzerinde filtreleme yapılabilme (içinde geçen veya çoklu filtreleme)
5• Retable üzerinde validasyon kuralları tanımlama (yetki dahilinde)
• Retable kartı üzerinden denetim(audit) ve tarihçe (history) bilgilerinin izlenmesi
• Değişikliklerin veri tabanına aktarılana kadar veya onay verecek kullanıcının
onaylamasına kadar ara bir katmanda saklanması
3.6 Form Yapısı
• Retable alanları üzerinde form yapısı kullanımı
• Kolon isimlerini düzenleyebilme, açıklama ekleyebilme, liste içerisinden seçim
yaptırabilme
• Formlar üzerinden veri giriş ve veritabanına kayıt
3.7 Validasyon Kuralları Yaklaşımı
3.7.1 Value Rules
• Belirli değerlere, aralıklara, karakter uzunluklarına, içerik yapısına göre veri giriş
uyarıları
3.7.2 Type Rules
• Takvim seçimi, tarih biçimi, ondalık basamak gibi tip bazlı kontroller
3.7.3 Mapping Rules
• Otomatik karakter düzeltmeleri (büyük/küçük harf, boşluk silme, Türkçe karakter
çevirme vb.)
3.7.4 Action Rules
• Otomatik veri doldurma (ID, tarih, kullanıcı adı (ekleyen & güncelleyen), dosya
adı, sayılar vs.)
3.7.5 Dependent Action Rules
• Bir kolondaki değere bağlı olarak başka bir kolonu otomatik güncelleme
3.8 Hiyerarşik Liste Desteği
• Bir liste alanının, başka bir alandaki seçime göre otomatik filtrelenmesi
3.9 Bildirim Yönetimi
• Veri tabanına kayıt eklendiğinde bildirim gönderimi
• Belirlenen zaman aralığında kayıt girişi yapılmadığında bildirim gönderimi
• Belirlenen zaman aralığında kayıt güncellemesi yapılmadığında bildirim
gönderimi
• Tabloda format değişikliği yapıldığında bildirim gönderimi
6• Tabloya yeni bir validasyon kuralı tanımlandığında bildirim gönderimi
3.10 History Yönetimi
• Retable’da yapılan işlemlerin tarihçesinin tutulması ve geri izlenebilirliği
3.11 Onay (Approve) Mekanizması
• Retable üzerinde yapılan işlemlerin onaylandıktan sonra gerçek tabloya
aktarılması
• Onay verecek kişinin toplu veya kayıt bazında onay verebilmesi
• Onay verecek kişinin toplu veya kayıt bazında yapılan değişiklikleri red edebilmesi
3.12 Role ve Yetkilendirme Yapısı
• Admin rolü
• Tabloların kullanıcılara özel atanması
• Kolon bazlı ve satır bazlı yetkilendirme
• Çoklu role atanması ve kullanıcıya çoklu rol tanımı yapılabilmesi
• Tarih bazlı tablo erişim yetkisi tanımı
3.13 Altyapı ve Güvenlik
• Rol tabanlı yetkilendirme sistemi bulunur.
• Güvenli kimlik doğrulama ve yetkilendirme sistemleri desteklenir.
• HTTPS protokolü kullanılarak güvenli erişim sağlanır.
4\. Teknik Altyapı Gereksinimleri
4.1 Genel Altyapı
• Web tabanlı mimari
• HTML/CSS/JavaScript ile kullanıcı arayüzü
• RDBMS (ilişkisel veritabanı) ile uyumlu yapı
4.2 Sunucu Gereksinimleri
Sunucu Rolü CPU RAM Disk OS Diğer Bileşenler
Uygulama Sunucusu 4 Core 16 GB 128 GB Linux Java 8, Tomcat 9
4.3 Ağ ve Erişim
• Web Arayüz: Port 9090
7• Servisler: Port 9093 (HTTPS)
• Tüm erişim HTTPS/SSL ile güvence altına alınmalıdır.
4.4 Veritabanı Gereksinimleri
• Oracle veya Postgre üzerine kurulum
• Meta data kurulumu ve sonuçların saklanması için veri tabanında 2 şema
gereklidir.
Schema Name Schema Types
RETOUCH Retouch Meta Tables
RETOUCH\_REP Retouch Result Tables
8
