{
  "recommendations": [
    // Java & Spring Boot
    "vscjava.vscode-java-pack",
    "vmware.vscode-spring-boot",
    "vscjava.vscode-spring-initializr",
    "vscjava.vscode-spring-boot-dashboard",
    "pivotal.vscode-boot-dev-pack",
    "redhat.java",
    "vscjava.vscode-java-debug",
    "vscjava.vscode-java-test",
    "vscjava.vscode-maven",
    // TypeScript & React
    "ms-vscode.vscode-typescript-next",
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "dsznajder.es7-react-js-snippets",
    "formulahendry.auto-rename-tag",
    "formulahendry.auto-close-tag",
    "bradlc.vscode-tailwindcss",
    "christian-kohler.npm-intellisense",
    "christian-kohler.path-intellisense",
    "styled-components.vscode-styled-components",
    "ms-vscode.typescript-javascript-grammar",
    "rvest.vs-code-prettier-eslint",
    "wix.vscode-import-cost",
    // i18n & Multilanguage Support
    "lokalise.i18n-ally",
    "mikestead.dotenv",
    "antfu.i18n-ally",
    // Docker
    "ms-azuretools.vscode-docker",
    "ms-vscode-remote.remote-containers",
    // Database
    "cweijan.vscode-postgresql-client2",
    // Git
    "eamodio.gitlens",
    "donjayamanne.githistory",
    // Productivity
    "visualstudioexptteam.vscodeintellicode",
    "streetsidesoftware.code-spell-checker",
    "usernamehw.errorlens",
    "wayou.vscode-todo-highlight",
    "gruntfuggly.todo-tree",
    "pkief.material-icon-theme",
    "oderwat.indent-rainbow",
    "aaron-bond.better-comments",
    "yzhang.markdown-all-in-one"
  ]
}
